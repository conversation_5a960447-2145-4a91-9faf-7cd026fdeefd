export default {
    getList(data: GetListParams) {
        return request({
            url: '/nebula/api/v1/external/documents',
            method: 'post',
            data
        });
    },
    create(data: CreateParams) {
        return request({
            url: '/nebula/api/v1/external/document/create',
            method: 'post',
            data
        });
    },
    update(data: CreateParams) {
        return request({
            url: '/nebula/api/v1/external/document/change',
            method: 'post',
            data
        });
    },
    plagiarismCheck(data: { ids: string[] }) {
        return request({
            url: '/nebula/api/v1/external/import/company/plagiarism-check',
            method: 'post',
            data
        });
    },
    log(params: InternalDocumentLogParams) {
        return request({
            url: '/nebula/api/v1/external/document/change-records',
            method: 'get',
            params
        });
    },
};
interface GetListParams {
    page?: number; //页码
    pageSize?: number; //每页条数
    noPage?: boolean; //是否分页
    number?: string; //文件编号
    name?: string; //文件名称
    originalNumber?: string; //原文件编号
    originalDocNumber?: string; //原文件发文号
    publishDocNumber?: string; //发文号
    publishDepartment?: string; //发文部门
    typeDictionaryNodeIds?: string[]; //文件类型
    domainDictionaryNodeId?: string; //所属领域
    authenticationDictionaryNodeId?: string; //认证方式
    beAttachedFile?: string; //是否附件
    status?: number; //状态
    orgType?: number; //组织类型
}
interface CreateParams {
    id?: string;
    name: string; //文件名称
    originalNumber: string; //原文件编号
    originalVersion: string; //原版本版次
    fileId?: string | null; //文件ID
    originalDocNumber: string | null; //原文件发文号
    publishDocNumber: string | null; //发文号
    publishDepartment: string | null; //发文部门
    publishDate: string | null; //发布日期
    effectiveDate: string | null; //实施日期
    orgType: number; //组织类型
    authenticationDictionaryNodeIds: any; //认证方式
    typeDictionaryNodeId: any; //文件类型
    domainDictionaryNodeId: string | null; //所属领域
}

export interface InternalDocumentLogParams {
    /**
     * 文档ID
     */
    documentId: string;
    /**
     * 是否不分页，默认为false
     */
    noPage?: boolean;
    /**
     * 页码，默认为1
     */
    page?: number;
    /**
     * 每页大小，默认为10，最大100000
     */
    pageSize?: number;
    [property: string]: any;
}