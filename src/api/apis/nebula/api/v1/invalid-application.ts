export default {
    // 获取作废申请列表
    getList(params: GetListParams) {
        return request({
            method: 'post',
            url: '/nebula/api/v1/document-library/invalid/list',
            data: params
        });
    },
    // 创建作废申请
    create(data: CreateInvalidApplicationParams) {
        return request({
            method: 'post',
            url: '/nebula/api/v1/document-library/invalid/create',
            data
        });
    },
    // 更新作废申请
    update(data: UpdateInvalidApplicationParams) {
        return request({
            method: 'post',
            url: '/nebula/api/v1/document-library/invalid/update',
            data
        });
    },
    // 获取作废申请详情
    getDetail(id: string) {
        return request({
            method: 'get',
            url: '/nebula/api/v1/document-library/invalid/detail',
            params: { id }
        });
    },
    // 暂存作废申请
    saveTemp(data: CreateInvalidApplicationParams) {
        return request({
            method: 'post',
            url: '/nebula/api/v1/document-library/invalid/save-temp',
            data
        });
    },
    // 删除作废申请
    delete(id: string) {
        return request({
            method: 'post',
            url: '/nebula/api/v1/document-library/invalid/delete',
            data: { id }
        });
    },
    // 撤销作废申请
    revoke(id: string) {
        return request({
            method: 'post',
            url: '/nebula/api/v1/document-library/invalid/revoke',
            data: { id }
        });
    }
};

export interface GetListParams {
    no?: string; // 文件编号
    name?: string; // 文件名称
    originalNo?: string; // 原文件编号
    docCategoryIds?: string[]; // 文件类别
    departmentIds?: string[]; // 编制部门
    status?: number; // 状态
    hasAttachment?: number; // 是否有附件
    page?: number;
    pageSize?: number;
    [property: string]: any;
}

export interface CreateInvalidApplicationParams {
    applicant: string; // 申请人
    applyDate: number; // 申请日期
    fileType: number; // 文件类型 1-内部文件 2-外部文件
    typeDictNodeId: string; // 文件类别ID
    reason: string; // 作废原因
    otherReason?: string; // 其他原因
    wishDistributeDate: number; // 拟定作废日期
    documents: InvalidDocumentItem[]; // 作废文件清单
    [property: string]: any;
}

export interface UpdateInvalidApplicationParams extends CreateInvalidApplicationParams {
    id: string; // 作废申请ID
}

export interface InvalidDocumentItem {
    id?: string; // 临时ID
    documentId: string; // 文件ID
    documentNo: string; // 文件编号
    documentVersionNo: string; // 版本版次
    [property: string]: any;
}
