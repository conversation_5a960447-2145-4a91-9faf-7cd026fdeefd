<template>
    <div class="change-log">
        <n-data-table :columns="columns" :data="data" size="small" :pagination="pagination" scroll-x="1200px">
            <template #departmentNames="{ row }">
                <span>{{ row.departmentNames?.join(',') }}</span>
            </template>
            <template #authorNames="{ row }">
                <span>{{ row.authorNames?.join(',') }}</span>
            </template>
            <template #table_auditors="{ row }">
                <n-space vertical size="small">
                    <span v-for="item in row.approvalInfo?.auditors" :key="item.id">
                        {{ `${item.userNickname}（${dayjs(item.passedDate).format('YYYY-MM-DD')}）` }}
                    </span>
                    <span v-if="row.approvalInfo?.auditors.length === 0" class="text-gray-400"> - </span>
                </n-space>
            </template>
            <template #table_approvers="{ row }">
                <n-space vertical size="small">
                    <span v-for="item in row.approvalInfo?.approvers" :key="item.id">
                        {{ `${item.userNickname}（${dayjs(item.passedDate).format('YYYY-MM-DD')}）` }}
                    </span>
                    <span v-if="row.approvalInfo?.approvers.length === 0" class="text-gray-400"> - </span>
                </n-space>
            </template>
            <template #publishDate="{ row }">
                <n-time :time="row.publishDate" format="yyyy-MM-dd" />
            </template>
            <template #effectiveDate="{ row }">
                <n-time :time="row.effectiveDate" format="yyyy-MM-dd" />
            </template>
            <template #operationType="{ row }">
                <n-tag v-bind="getOperationTypeConfig(row.operationType)" round size="small" :bordered="false">
                    {{ getOperationTypeConfig(row.operationType).text }}
                </n-tag>
            </template>
            <template #todo="{ row }">
                <n-button type="primary" size="tiny" @click="handleView(row)">详情</n-button>
            </template>
        </n-data-table>
    </div>
</template>

<script setup lang="ts">
import { PaginationProps } from 'naive-ui';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';
import dayjs from 'dayjs';

const props = withDefaults(
    defineProps<{
        id?: string;
        type: number; //  2-内部库 3-外部库
    }>(),
    {
        id: ''
    }
);

const columns = ref<TableColumns>([
    {
        title: '序号',
        key: 'index',
        align: 'center',
        width: 60
    },
    {
        title: '文件编号',
        key: 'documentNo',
        align: 'center',
        minWidth: 200,
        fixed: 'left',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '版本/版次',
        key: 'documentVersion',
        align: 'center',
        width: 80
    },
    {
        title: '文件名称',
        key: 'documentName',
        align: 'center',
        minWidth: 200,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '文件类别',
        key: 'documentCategory',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '编制部门',
        key: 'departmentNames',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '编制人',
        key: 'authorNames',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '审核人',
        key: 'auditors',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '批准人',
        key: 'approvers',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发布日期',
        key: 'publishDate',
        align: 'center',
        width: 120,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '实施日期',
        key: 'effectiveDate',
        align: 'center',
        width: 120,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '操作类型',
        key: 'operationType',
        align: 'center',
        width: 80,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '查看详情',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const data = ref([]);

const getChangeLog = async () => {
    try {
        let res;
        // 根据type调用不同的接口
        switch (props.type) {
            case 2: // 书籍库
                // 暂时使用内部接口，后续可以添加书籍库专用接口
                res = await $apis.nebula.api.v1.internal.log({ documentId: props.id, noPage: true });
                break;
            case 3: // 内部库
                res = await $apis.nebula.api.v1.external.log({ documentId: props.id, noPage: true });
                break;
        }
        data.value = res.data?.data || [];
    } catch (error) {
        console.error('获取变更记录失败:', error);
        data.value = [];
    }
};

const pagination = ref<PaginationProps>({
    pageSize: 10,
    prefix: ({ itemCount }) => `共 ${itemCount} 条`
});

// 操作类型配置映射
const getOperationTypeConfig = (operationType: number) => {
    const configMap = {
        0: { text: '无', type: 'default' },
        1: { text: '新增', type: 'success' },
        2: { text: '修订', type: 'primary' },
        3: { text: '作废', type: 'error' }
    } as const;

    return configMap[operationType as keyof typeof configMap] || { text: '未知', type: 'default' };
};

const handleView = (row: any) => {
    window.$message.info(`变更记录：${row.time}`);
};

onMounted(() => {
    getChangeLog();
});
</script>

<style scoped lang="less"></style>
