<template>
    <div class="invalid-application">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 1600,
                maxHeight: 'calc(100vh - 440px)'
            }"
            :params="params"
            :data-api="
                $apis.test.mockList.bind(null, {
                    'no|1': ['1234567890', '1234567891', '1234567892'],
                    'name|1': ['文件名称1', '文件名称2', '文件名称3'],
                    'originalNo|1': ['1234567890', '1234567891'],
                    'docCategoryName|1': ['文件类别1', '文件类别2', '文件类别3'],
                    'invalidFileCount|1': [1, 2, 3],
                    'invalidDate|1': [1735689600000, 1735776000000, 1735862400000],
                    'applicant|1': ['申请人1', '申请人2', '申请人3'],
                    'applyDate|1': [1735689600000, 1735776000000, 1735862400000],
                    'auditors|1': ['审核人1', '审核人2', '审核人3'],
                    'approvers|1': ['批准人1', '批准人2', '批准人3'],
                    'status|1': [1, 2, 3]
                })
            "
            :search-props="{
                showAdd: store.permissions.indexOf('internalInvalidFileAdd') > -1,
                showInput: false,
                searchInputPlaceholder: '请输入文件编号、名称 / 原文件编号',
                inputWidth: '280px'
            }"
            :search-table-space="{
                size: 20
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            @add="handleModel(null, false)"
            @reset="handleReset"
        >
            <template #search_form_middle>
                <n-input class="w-198px" v-model:value="params.no" placeholder="请输入文件编号" />
                <n-input class="w-198px" v-model:value="params.name" placeholder="请输入文件名称" />
            </template>
            <template #search_form_after>
                <n-button type="primary" @click="show = !show">{{ show ? '收起' : '更多' }}</n-button>
            </template>
            <template #search_bottom_layout>
                <n-collapse-transition v-if="show" :show="show">
                    <n-space>
                        <n-input class="w-198px" v-model:value="params.originalNo" placeholder="请输入原文件编号" />
                        <select-tree-dictionary
                            class="w-198px"
                            v-model:value="params.docCategoryIds"
                            placeholder="选择文件类别"
                            multiple
                            style="width: 200px"
                            checkable
                            filterable
                            clearable
                            cascade
                            :show-path="false"
                        />
                        <select-tree-organization
                            class="w-198px"
                            v-model:value="params.departmentIds"
                            multiple
                            checkable
                            filterable
                            cascade
                            :show-path="false"
                            maxTagCount="responsive"
                            placeholder="选择编制部门"
                        />
                        <n-select
                            class="w-140px"
                            v-model:value="params.hasAttachment"
                            :options="$datas.fileLibrary.hasAttachmentOptions"
                            clearable
                            placeholder="是否有附件"
                        />
                    </n-space>
                </n-collapse-transition>
            </template>
            <template #search_handle_after>
                <n-permission has="internalInvalidFileExport">
                    <n-button type="warning" @click="handleExport">导出</n-button>
                </n-permission>
            </template>

            <template #table_invalidDate="{ row }">
                <n-time :time="row.invalidDate" format="yyyy-MM-dd" />
            </template>
            <template #table_applyDate="{ row }">
                <n-time :time="row.applyDate" format="yyyy-MM-dd" />
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center" :wrap="false">
                    <n-permission has="internalInvalidFileDetail">
                        <n-button size="tiny" @click="handleModel(row, true)">详情</n-button>
                    </n-permission>
                    <n-permission has="internalInvalidFileRevoke">
                        <n-button size="tiny" type="success" @click="handleModel(row, false)">编辑</n-button>
                    </n-permission>
                    <n-permission has="internalInvalidFileRevoke">
                        <n-button size="tiny" type="primary" @click="handleRevoke(row)">撤销</n-button>
                    </n-permission>
                    <n-permission has="internalInvalidFileDelete">
                        <n-button size="tiny" type="error" @click="handleDelete(row)">删除</n-button>
                    </n-permission>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { type DataTableColumns } from 'naive-ui';
import useStore from '@/store/modules/main';

const store = useStore();

const searchTablePageRef = ref();
const params = ref<any>({
    no: '', // 文件编号
    name: '', // 文件名称
    originalNo: '', // 原文件编号
    docCategoryIds: [], // 文件类别
    departmentIds: null, // 编制部门
    status: null, // 状态
    hasAttachment: null // 是否有附件
});

const show = ref(false);

const columns: DataTableColumns = [
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 60,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '文件编号', key: 'no', align: 'center', fixed: 'left', ellipsis: { tooltip: true }, resizable: true },
    { title: '文件类别', key: 'docCategoryName', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '作废文件数', key: 'invalidFileCount', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '拟定作废日期', key: 'invalidDate', align: 'center', width: 120, resizable: true },
    { title: '申请人', key: 'applicant', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '申请日期', key: 'applyDate', align: 'center', width: 120, resizable: true },
    { title: '审核人', key: 'auditors', align: 'center', ellipsis: { tooltip: true } },
    { title: '批准人', key: 'approvers', align: 'center', ellipsis: { tooltip: true } },
    { title: '状态', key: 'status', align: 'center', ellipsis: { tooltip: true } },
    { title: '操作', key: 'todo', align: 'center', fixed: 'right', width: 160 }
];

const init = () => {
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};

const handleReset = () => {
    params.value = {
        no: '',
        name: '',
        originalNo: '',
        departmentIds: null,
        status: null,
        hasAttachment: null,
        docCategoryIds: params.value.docCategoryIds || []
    };
    init();
};

const handleExport = () => {
    window.$message.info('点击导出');
};

/**
 * 新增、编辑、详情
 */
const handleModel = (row: any, isPreview = false) => {
    console.log('编辑', row);
    $alert.dialog({
        title: '编辑',
        width: '60%',
        content: import('./models/invalid-form.vue'),
        props: {
            row,
            isPreview,
            onSubmit: () => init()
        }
    });
};

/**
 * 撤销操作
 */
const handleRevoke = (row: any) => {
    console.log('撤销', row);
};

/**
 * 删除操作
 */
const handleDelete = (row: any) => {
    console.log('删除', row);
};
</script>
