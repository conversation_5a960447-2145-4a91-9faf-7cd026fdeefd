<template>
    <alert-content :buttons="buttons">
        <n-form
            class="mt-2"
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            label-width="110px"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="12" :x-gap="16">
                <n-form-item-gi :span="12" label="申请人">
                    <n-input v-model:value="formData.applicant" readonly />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="申请日期">
                    <n-input :value="dayjs(formData.applyDate).format('YYYY-MM-DD')" readonly />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="拟定作废日期" path="wishDistributeDate">
                    <n-date-picker
                        v-model:value="formData.wishDistributeDate"
                        type="date"
                        placeholder="请选择希望发放日期"
                        clearable
                        format="yyyy-MM-dd"
                        style="width: 100%"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="文件类型" path="fileType">
                    <n-select
                        v-model:value="formData.fileType"
                        :options="fileTypeOptions"
                        placeholder="请选择文件类型"
                        clearable
                        @update:value="onFileTypeChange"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="文件类别" path="typeDictNodeId">
                    <SelectTreeDictionary
                        v-model:value="formData.typeDictNodeId"
                        placeholder="请选择文件类别"
                        clearable
                        filterable
                        :disabled="!formData.fileType"
                        :need-path-info="true"
                        @change="onTypeDictNodeIdChange"
                        :formData="formData.fileType === 1 ? 'internal' : formData.fileType === 2 ? 'external' : ''"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="作废原因" path="reason">
                    <n-select
                        v-model:value="formData.reason"
                        :options="reasonOptions"
                        placeholder="请选择发放原因"
                        clearable
                    />
                </n-form-item-gi>
                <n-form-item-gi v-if="formData.reason === '其他'" :span="24" label="其他原因" path="otherReason">
                    <n-input
                        v-model:value="formData.otherReason"
                        maxlength="50"
                        placeholder="请输入其他原因"
                        show-count
                    />
                </n-form-item-gi>

                <n-form-item-gi :span="24" path="distributeList">
                    <div class="flex-v w-100%">
                        <div class="flex justify-between">
                            <span class="ml-32px mb-10px required-field">发放清单</span>
                            <n-button
                                type="primary"
                                size="tiny"
                                :disabled="!formData.typeDictNodeId"
                                @click="handleAddFile"
                            >
                                增加文件
                            </n-button>
                        </div>
                        <vxe-table
                            ref="tableRef"
                            class="w-100%"
                            :border="true"
                            show-overflow
                            auto-resize
                            :edit-rules="validRules"
                            :valid-config="{ showMessage: false }"
                            :edit-config="{
                                trigger: 'click',
                                mode: 'cell',
                                enabled: !isPreview
                            }"
                            :data="formData.documents"
                        >
                            <vxe-column type="seq" title="序号" width="70" fixed="left"></vxe-column>
                            <vxe-column
                                field="documentId"
                                title="文件名称"
                                minWidth="120"
                                :edit-render="{}"
                                :formatter="({ row }) => formatOptionLabel(row.id, 'documentIdOptions', row.documentId)"
                            >
                                <template #edit="{ row }">
                                    <vxe-select
                                        v-model="row.documentId"
                                        :options="documentIdOptions"
                                        @change="handleDocumentIdChange()"
                                        clearable
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column field="documentNo" title="文件编号" minWidth="120"> </vxe-column>
                            <vxe-column field="documentVersionNo" title="版本/版次" minWidth="120"> </vxe-column>
                            <vxe-column field="todo" title="操作" width="80" fixed="right">
                                <template v-slot="{ row }">
                                    <n-button v-if="!isPreview" size="tiny" type="error" @click="handleRemoveFile(row)">
                                        删除
                                    </n-button>
                                </template>
                            </vxe-column>
                        </vxe-table>
                    </div>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { FormRules } from 'naive-ui';
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';
import type { ButtonsConfig } from '@/components/alert-content.vue';
import { RowVO } from '@/api/sass/api/v1/dict';
import { VxeTablePropTypes } from 'vxe-table';

const props = defineProps<{
    row: any;
    isPreview: boolean;
}>();

const store = useStore();

const buttons: ButtonsConfig = {
    save: {
        text: '提交',
        onClick() {
            return onSubmit();
        }
    },
    extra: {
        saveTemp: {
            text: '暂存',
            type: 'success',
            onClick: () => {
                return onSubmit('saveTemp');
            },
            emit: 'saveTemp',
            autoClose: true
        }
    }
};

const fileTypeOptions = [
    { label: '内部文件', value: 1 },
    { label: '外部文件', value: 2 }
];
const reasonOptions = [
    { label: '新员工入职', value: '新员工入职' },
    { label: '岗位/职责调整', value: '岗位/职责调整' },
    { label: '文件版本更新', value: '文件版本更新' },
    { label: '新增业务/流程实施', value: '新增业务/流程实施' },
    { label: '跨部门协作需求', value: '跨部门协作需求' },
    { label: '其他', value: '其他' }
];

const formData = reactive({
    applicant: store.userInfo.nickname || '自动生成',
    applyDate: dayjs().valueOf(),
    distributeType: null,
    fileType: null,
    typeDictNodeId: null,
    reason: null,
    otherReason: null,
    wishDistributeDate: null,
    documents: [] as any
});

const rules = computed<FormRules>(() => {
    return {
        distributeType: { required: true, type: 'number', message: '请选择发放类型', trigger: 'change' },
        fileType: { required: true, type: 'number', message: '请选择文件类型', trigger: 'change' },
        typeDictNodeId: { required: true, message: '请选择文件类别', trigger: 'change' },
        reason: { required: true, message: '请选择发放原因', trigger: 'change' },
        otherReason: { required: formData.reason === '其他', message: '请输入其他原因', trigger: 'blur' },
        distributeList: [
            {
                required: true,
                validator: () => {
                    if (!formData.documents.length) {
                        return new Error('请填写作废清单');
                    }
                    return true;
                }
            }
        ]
    };
});

const onFileTypeChange = () => {
    formData.typeDictNodeId = null;
    // 清空发放清单
    formData.documents = [];
};

const onTypeDictNodeIdChange = () => {
    // 清空发放清单
    formData.documents = [];
};

/**
 * vxe-table
 */
const handleAddFile = () => {
    formData.documents.push({
        fileName: '',
        filePath: ''
    });
};
// 删除文件
const handleRemoveFile = (row: any) => {
    const index = formData.documents?.findIndex((item: any) => item.id === row.id);
    console.log('🚀 ~ handleRemoveFile ~ index:', index);
};

const documentIdOptions = computed(() => {
    return [];
});
const handleDocumentIdChange = () => {
    // 清空发放清单
};

// 表格验证
const validRules = ref<VxeTablePropTypes.EditRules<RowVO>>({
    documentId: [{ required: true, message: '请选择文件' }],
    documentNo: [{ required: true, message: '请输入文件编号' }],
    documentVersionNo: [{ required: true, message: '请输入文件版本' }]
});

// 格式化选项标签 - 将 id 转换为对应的 label 显示
const formatOptionLabel = (rowId: string, optionType: string, value: string): string => {
    if (!value) return '';
    return value;
};

const onSubmit = async (type: 'submit' | 'saveTemp' = 'submit') => {
    try {
        await formRef.value?.validate();
        await $utils.vxeTableConfig.tableValid(tableRef);
    } catch (err: any) {
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }
    console.log('🚀 ~ onSubmit ~ type:', type);
};

const formRef = ref();
const tableRef = ref();
</script>

<style scoped lang="less">
/* 添加必填星号样式 */
.required-field::before {
    content: '*';
    color: var(--n-asterisk-color);
    margin-right: 4px;
}
</style>
